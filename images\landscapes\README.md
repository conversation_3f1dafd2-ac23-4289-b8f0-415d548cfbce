# Landscape Images

This directory contains the background images used for the Daily Verse app.

## Image Requirements

- **Format**: JPG or WebP (recommended for smaller file sizes)
- **Dimensions**: Minimum 1920x1080 (Full HD), ideally 2560x1440 or higher
- **Aspect Ratio**: 16:9 or wider landscape orientation
- **File Size**: Keep under 500KB each for optimal loading performance
- **Content**: Peaceful, nature-focused landscapes that work well with text overlay

## Current Images

The app expects these image files (as defined in `/data/images.json`):

1. `forest-morning.jpg` - Peaceful morning forest with sunlight
2. `mountain-lake.jpg` - Serene mountain lake reflecting peaks
3. `ocean-sunset.jpg` - Golden sunset over calm ocean
4. `rolling-hills.jpg` - Green rolling hills under blue sky
5. `desert-sunrise.jpg` - Desert landscape with warm sunrise
6. `meadow-flowers.jpg` - Wildflower meadow with mountains
7. `river-valley.jpg` - Winding river through green valley
8. `autumn-path.jpg` - Forest path with autumn leaves

## Adding New Images

1. Add your image files to this directory
2. Update `/data/images.json` with the new image information:
   ```json
   {
     "src": "/images/landscapes/your-image.jpg",
     "credit": "Photo by [Photographer] on [Source]",
     "alt": "Description of the image for accessibility"
   }
   ```

## Image Sources

For free, high-quality landscape images, consider:

- **Unsplash** (https://unsplash.com) - Free high-resolution photos
- **Pexels** (https://pexels.com) - Free stock photos
- **Pixabay** (https://pixabay.com) - Free images and photos

## Attribution

Always provide proper attribution for images in the `credit` field. The app will display photo credits in the footer.

## Optimization Tips

- Use tools like TinyPNG or ImageOptim to compress images
- Consider using WebP format for better compression
- Test images with the dark overlay to ensure text remains readable
- Ensure images work well across different screen sizes and orientations
