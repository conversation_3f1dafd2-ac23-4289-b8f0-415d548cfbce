<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Versaily | Your Daily Bible Verse</title>
    <meta name="description" content="A new Bible verse every day with a short, clear explanation.">
    <meta property="og:title" content="Daily Verse">
    <meta property="og:description" content="A new Bible verse every day with a short, clear explanation.">
    <meta property="og:image" content="/og.jpg">
    <meta property="og:type" content="website">
    
    <!-- Preconnect for performance -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

    <!-- Google Fonts - Libre Baskerville -->
    <link href="https://fonts.googleapis.com/css2?family=Libre+Baskerville:ital,wght@0,400;0,700;1,400&display=swap" rel="stylesheet">

    <!-- FontAwesome Icons -->
    <script src="https://kit.fontawesome.com/d643140327.js" crossorigin="anonymous"></script>

    <!-- Inline critical CSS will be added here -->
    <link rel="stylesheet" href="styles.css">
    
    <!-- Favicon -->
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>📖</text></svg>">
</head>
<body>
    <!-- Main Hero Section -->
    <main class="hero" id="hero" role="main">
        <div class="hero-overlay"></div>
        <div class="hero-content">
            <header class="verse-header">
                <time class="date" id="date" datetime="">
                    Loading...
                </time>
            </header>
            
            <section class="verse-section" aria-labelledby="verse-text">
                <blockquote class="verse-text" id="verse-text">
                    Loading today's verse...
                </blockquote>
                <cite class="verse-ref" id="verse-ref">
                    <!-- Reference will be loaded here -->
                </cite>
            </section>
            
            <div class="action-buttons">
                <button
                    class="btn btn-explain"
                    id="explain-btn"
                    aria-label="What does this verse mean?"
                    type="button"
                >
                    <i class="fa-light fa-lightbulb btn-icon" aria-hidden="true"></i>
                    <span class="btn-text">Get Insight</span>
                </button>

                <button
                    class="btn btn-share"
                    id="share-btn"
                    aria-label="Copy verse to clipboard"
                    type="button"
                >
                    <i class="fa-light fa-copy btn-icon" aria-hidden="true"></i>
                    <span class="btn-text">Share Verse</span>
                </button>
            </div>
        </div>
    </main>

    <!-- Explanation Modal -->
    <div 
        class="modal-overlay" 
        id="modal-overlay" 
        role="dialog" 
        aria-labelledby="modal-title"
        aria-hidden="true"
    >
        <div class="modal-content">
            <header class="modal-header">
                <h2 class="modal-title" id="modal-title">
                    Understanding this verse
                </h2>
                <button
                    class="modal-close"
                    id="modal-close"
                    aria-label="Close explanation"
                    type="button"
                >
                    <i class="fas fa-times" aria-hidden="true"></i>
                </button>
            </header>
            
            <div class="modal-body" id="modal-body">
                <div class="explanation-loading" id="explanation-loading">
                    <div class="loading-spinner" aria-hidden="true"></div>
                    <p>Loading explanation...</p>
                </div>
                
                <div class="explanation-content" id="explanation-content" style="display: none;">
                    <section class="explanation-section">
                        <h3>Context</h3>
                        <p id="explanation-context"></p>
                    </section>
                    
                    <section class="explanation-section">
                        <h3>Core Message</h3>
                        <p id="explanation-core"></p>
                    </section>
                    
                    <section class="explanation-section">
                        <h3>Modern Application</h3>
                        <p id="explanation-application"></p>
                    </section>
                </div>
                
                <div class="explanation-error" id="explanation-error" style="display: none;">
                    <p>Sorry, we couldn't load the explanation right now. Please try again later.</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Toast Notification -->
    <div class="toast" id="toast" role="alert" aria-live="polite">
        <span class="toast-message" id="toast-message"></span>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="footer-content">
            <p class="copyright">
                © 2025 Versaily
            </p>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="app.js" defer></script>
</body>
</html>
