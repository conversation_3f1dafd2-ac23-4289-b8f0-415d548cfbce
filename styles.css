/* CSS Custom Properties */
:root {
  --color-white: #ffffff;
  --color-black: #000000;
  --color-overlay: rgba(0, 0, 0, 0.45);
  --color-modal-bg: rgba(0, 0, 0, 0.8);
  --color-text-light: rgba(255, 255, 255, 0.9);
  --color-text-dim: rgba(255, 255, 255, 0.7);
  --color-button-bg: rgba(255, 255, 255, 0.15);
  --color-button-hover: rgba(255, 255, 255, 0.25);
  --color-button-focus: rgba(255, 255, 255, 0.3);

  --font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-family-serif: 'Libre Baskerville', 'Times New Roman', serif;
  --font-size-base: 1rem;
  --font-size-lg: 1.25rem;
  --font-size-xl: 1.5rem;
  --font-size-2xl: 2rem;
  --font-size-3xl: 2.5rem;

  --spacing-xs: 0.5rem;
  --spacing-sm: 1rem;
  --spacing-md: 1.5rem;
  --spacing-lg: 2rem;
  --spacing-xl: 3rem;

  --border-radius: 0.5rem;
  --transition: all 0.2s ease;
}

/* Reset and Base Styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  height: 100%;
  font-size: 16px;
}

body {
  font-family: var(--font-family);
  line-height: 1.6;
  color: var(--color-white);
  background: var(--color-black);
  min-height: 100vh;
  overflow-x: hidden;
}

#hero {
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  background-attachment: fixed;
}

/* Hero Section */
.hero {
  position: relative;
  min-height: 100vh;
  background-size: cover;
  display: flex;
  align-items: center;
  justify-content: center;
  background-position: center;
  background-repeat: no-repeat;
  background-attachment: fixed;
  /* Fallback gradient background */
  background: linear-gradient(135deg, #2d5016 0%, #3e6b1f 25%, #4a7c23 50%, #3e6b1f 75%, #2d5016 100%);
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(0, 0, 0, 0.4) 0%,
    rgba(0, 0, 0, 0.6) 50%,
    rgba(0, 0, 0, 0.4) 100%
  );
  z-index: 1;
}

.hero-content {
  position: relative;
  z-index: 2;
  text-align: center;
  max-width: 1000px;
  padding: var(--spacing-xl);
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  justify-content: center;
  min-height: 100vh;
}

/* Typography */
.date {
  font-size: 0.875rem;
  color: var(--color-text-dim);
  font-weight: 400;
  letter-spacing: 0.1em;
  text-transform: uppercase;
  margin-bottom: var(--spacing-lg);
  display: block;
  opacity: 0.8;
}

.verse-text {
  font-family: var(--font-family-serif);
  font-size: clamp(1.75rem, 4vw, 3rem);
  font-style: italic;
  font-weight: 400;
  line-height: 1.3;
  color: var(--color-white);
  margin-bottom: var(--spacing-lg);
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.4);
  max-width: 900px;
  margin-left: auto;
  margin-right: auto;
}

.verse-ref {
  font-size: 1rem;
  font-weight: 400;
  color: var(--color-text-dim);
  letter-spacing: 0.05em;
  margin-bottom: var(--spacing-xl);
  display: block;
  opacity: 0.9;
}

/* Buttons */
.action-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
  margin-top: var(--spacing-xl);
}

.btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.25);
  border-radius: 2rem;
  color: var(--color-white);
  font-family: inherit;
  font-size: 0.875rem;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  min-width: 140px;
  justify-content: center;
}

.btn:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.btn:focus {
  outline: 2px solid rgba(255, 255, 255, 0.5);
  outline-offset: 2px;
  background: rgba(255, 255, 255, 0.2);
}

.btn:active {
  transform: translateY(0);
}

.btn-icon {
  font-size: 1em;
  opacity: 0.9;
}

/* Modal */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--color-modal-bg);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: var(--transition);
  backdrop-filter: blur(5px);
}

.modal-overlay.active {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  background: var(--color-white);
  color: var(--color-black);
  border-radius: var(--border-radius);
  max-width: 600px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  transform: scale(0.9) translateY(20px);
  transition: var(--transition);
}

.modal-overlay.active .modal-content {
  transform: scale(1) translateY(0);
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md) var(--spacing-lg);
  border-bottom: 1px solid #eee;
}

.modal-title {
  font-size: var(--font-size-xl);
  font-weight: 600;
  margin: 0;
}

.modal-close {
  background: none;
  border: none;
  font-size: var(--font-size-2xl);
  cursor: pointer;
  padding: var(--spacing-xs);
  border-radius: var(--border-radius);
  transition: var(--transition);
  color: #666;
}

.modal-close:hover {
  background: #f5f5f5;
  color: var(--color-black);
}

.modal-body {
  padding: var(--spacing-lg);
}

.explanation-section {
  margin-bottom: var(--spacing-md);
}

.explanation-section h3 {
  font-size: var(--font-size-lg);
  font-weight: 600;
  margin-bottom: var(--spacing-xs);
  color: #333;
}

.explanation-section p {
  color: #666;
  line-height: 1.6;
}

/* Loading Spinner */
.explanation-loading {
  text-align: center;
  padding: var(--spacing-lg);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #333;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto var(--spacing-sm);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Toast */
.toast {
  position: fixed;
  bottom: var(--spacing-lg);
  left: 50%;
  transform: translateX(-50%) translateY(100px);
  background: rgba(0, 0, 0, 0.9);
  color: var(--color-white);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--border-radius);
  font-size: var(--font-size-base);
  z-index: 1001;
  transition: var(--transition);
  backdrop-filter: blur(10px);
}

.toast.show {
  transform: translateX(-50%) translateY(0);
}

/* Footer */
.footer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 2;
  padding: var(--spacing-md);
  text-align: center;
}

.footer-content {
  font-size: 0.75rem;
  color: var(--color-text-dim);
  opacity: 0.7;
  line-height: 1.4;
}

.photo-credit {
  margin-bottom: 0.25rem;
}

.photo-credit a {
  color: inherit;
  text-decoration: none;
  border-bottom: 1px solid rgba(255, 255, 255, 0.3);
  transition: var(--transition);
}

.photo-credit a:hover {
  opacity: 1;
  border-bottom-color: rgba(255, 255, 255, 0.6);
}

/* Subtle entrance animation */
.hero-content {
  animation: fadeInUp 1s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-content {
    padding: var(--spacing-lg);
    min-height: 100vh;
  }

  .verse-text {
    font-size: clamp(1.5rem, 5vw, 2.5rem);
    line-height: 1.4;
  }

  .date {
    font-size: 0.8rem;
    margin-bottom: var(--spacing-md);
  }

  .verse-ref {
    font-size: 0.9rem;
    margin-bottom: var(--spacing-lg);
  }

  .action-buttons {
    flex-direction: column;
    align-items: center;
    gap: 0.75rem;
    margin-top: var(--spacing-lg);
  }

  .btn {
    width: 100%;
    max-width: 240px;
    padding: 0.875rem 1.5rem;
  }

  .modal-content {
    width: 95%;
    margin: var(--spacing-sm);
    max-height: 85vh;
  }
}

@media (max-width: 480px) {
  .hero-content {
    padding: var(--spacing-md);
  }

  .verse-text {
    font-size: clamp(1.25rem, 6vw, 2rem);
    margin-bottom: var(--spacing-md);
  }

  .date {
    font-size: 0.75rem;
  }

  .verse-ref {
    font-size: 0.85rem;
  }

  .modal-header {
    padding: var(--spacing-sm) var(--spacing-md);
  }

  .modal-body {
    padding: var(--spacing-md);
  }

  .btn {
    max-width: 200px;
    font-size: 0.8rem;
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Focus styles for keyboard navigation */
.btn:focus-visible,
.modal-close:focus-visible {
  outline: 2px solid #0066cc;
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .hero-overlay {
    background: linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.7));
  }
  
  .btn {
    border-width: 2px;
  }
}
