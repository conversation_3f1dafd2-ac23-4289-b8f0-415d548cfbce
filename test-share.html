<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Share Function</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .btn { padding: 10px 20px; margin: 10px; border: none; border-radius: 5px; cursor: pointer; }
        .btn-icon { margin-right: 8px; }
        #toast { position: fixed; top: 20px; right: 20px; background: #333; color: white; padding: 10px; border-radius: 5px; display: none; }
        #toast.show { display: block; }
    </style>
</head>
<body>
    <h1>Test Share Function</h1>
    <button class="btn" onclick="testCopy()">
        <i class="fas fa-copy btn-icon"></i>
        Test Copy
    </button>
    
    <div id="toast">
        <span id="toast-message"></span>
    </div>

    <script>
        async function testCopy() {
            const shareText = `"Test verse text" - Test Reference\n\nversaily.com`;
            
            try {
                if (navigator.clipboard && window.isSecureContext) {
                    await navigator.clipboard.writeText(shareText);
                    showToast('Text copied to clipboard');
                } else {
                    fallbackCopyToClipboard(shareText);
                    showToast('Text copied to clipboard');
                }
            } catch (error) {
                console.error('Copy failed:', error);
                showToast('Copy failed');
            }
        }

        function fallbackCopyToClipboard(text) {
            const textArea = document.createElement('textarea');
            textArea.value = text;
            textArea.style.position = 'fixed';
            textArea.style.left = '-999999px';
            textArea.style.top = '-999999px';
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();
            
            try {
                document.execCommand('copy');
            } finally {
                document.body.removeChild(textArea);
            }
        }

        function showToast(message) {
            const toast = document.getElementById('toast');
            const toastMessage = document.getElementById('toast-message');
            
            toastMessage.textContent = message;
            toast.classList.add('show');

            setTimeout(() => {
                toast.classList.remove('show');
            }, 3000);
        }
    </script>
</body>
</html>
