// Daily Verse App - Core Functionality
class DailyVerseApp {
  constructor() {
    this.verses = [];
    this.images = [];
    this.currentVerse = null;
    this.currentImage = null;
    this.startDate = new Date('2025-01-01');
    this.seed = 'dailyverse-v1';
    
    this.init();
  }

  async init() {
    try {
      await this.loadData();
      this.setupEventListeners();
      this.displayTodaysVerse();
    } catch (error) {
      console.error('Failed to initialize app:', error);
      this.showError('Failed to load daily verse. Please refresh the page.');
    }
  }

  async loadData() {
    try {
      const [versesResponse, imagesResponse] = await Promise.all([
        fetch('/data/verses.json'),
        fetch('/data/images.json')
      ]);

      if (!versesResponse.ok || !imagesResponse.ok) {
        throw new Error('Failed to fetch data');
      }

      this.verses = await versesResponse.json();
      this.images = await imagesResponse.json();
    } catch (error) {
      throw new Error('Data loading failed: ' + error.message);
    }
  }

  setupEventListeners() {
    // Explain button
    const explainBtn = document.getElementById('explain-btn');
    explainBtn?.addEventListener('click', () => this.showExplanation());

    // Share button
    const shareBtn = document.getElementById('share-btn');
    shareBtn?.addEventListener('click', () => this.shareVerse());

    // Modal close
    const modalClose = document.getElementById('modal-close');
    const modalOverlay = document.getElementById('modal-overlay');
    
    modalClose?.addEventListener('click', () => this.closeModal());
    modalOverlay?.addEventListener('click', (e) => {
      if (e.target === modalOverlay) this.closeModal();
    });

    // Keyboard navigation
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape') this.closeModal();
    });
  }

  displayTodaysVerse() {
    const todaysVerse = this.getTodaysVerse();
    const todaysImage = this.getTodaysImage(todaysVerse.id);
    
    this.currentVerse = todaysVerse;
    this.currentImage = todaysImage;

    // Update date
    const dateElement = document.getElementById('date');
    if (dateElement) {
      const today = new Date();
      const formattedDate = today.toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
      dateElement.textContent = formattedDate;
      dateElement.setAttribute('datetime', today.toISOString().split('T')[0]);
    }

    // Update verse content
    const verseText = document.getElementById('verse-text');
    const verseRef = document.getElementById('verse-ref');
    
    if (verseText) verseText.textContent = todaysVerse.text;
    if (verseRef) verseRef.textContent = `${todaysVerse.ref} (${todaysVerse.translation})`;

    // Update background image
    const hero = document.getElementById('hero');
    if (hero && todaysImage) {
      hero.style.backgroundImage = `url('${todaysImage.src}')`;
    }

    // Update photo credit
    const photoCredit = document.getElementById('photo-credit');
    if (photoCredit && todaysImage) {
      photoCredit.innerHTML = todaysImage.credit;
    }
  }

  getTodaysVerse() {
    const today = new Date();
    const todayUTC = new Date(today.getUTCFullYear(), today.getUTCMonth(), today.getUTCDate());
    const daysSinceStart = Math.floor((todayUTC - this.startDate) / (24 * 60 * 60 * 1000));
    
    // Create deterministic order using seeded shuffle
    const order = this.seededFisherYatesShuffle(
      this.verses.map(v => v.id), 
      this.seed
    );
    
    const index = daysSinceStart % order.length;
    const todaysId = order[index];
    
    return this.verses.find(v => v.id === todaysId) || this.verses[0];
  }

  getTodaysImage(verseId) {
    if (this.images.length === 0) return null;
    
    const hash = this.hashString(verseId + this.seed);
    const index = Math.abs(hash) % this.images.length;
    
    return this.images[index];
  }

  // Seeded Fisher-Yates shuffle for deterministic randomization
  seededFisherYatesShuffle(array, seed) {
    const arr = [...array];
    const rng = this.createSeededRNG(seed);
    
    for (let i = arr.length - 1; i > 0; i--) {
      const j = Math.floor(rng() * (i + 1));
      [arr[i], arr[j]] = [arr[j], arr[i]];
    }
    
    return arr;
  }

  // Simple seeded random number generator (Mulberry32)
  createSeededRNG(seed) {
    let h = this.hashString(seed);
    return function() {
      h = Math.imul(h ^ h >>> 15, h | 1);
      h ^= h + Math.imul(h ^ h >>> 7, h | 61);
      return ((h ^ h >>> 14) >>> 0) / 4294967296;
    };
  }

  // Simple string hash function (djb2)
  hashString(str) {
    let hash = 5381;
    for (let i = 0; i < str.length; i++) {
      hash = ((hash << 5) + hash) + str.charCodeAt(i);
    }
    return hash;
  }

  async showExplanation() {
    const modal = document.getElementById('modal-overlay');
    const modalTitle = document.getElementById('modal-title');
    const loadingDiv = document.getElementById('explanation-loading');
    const contentDiv = document.getElementById('explanation-content');
    const errorDiv = document.getElementById('explanation-error');

    if (!modal || !this.currentVerse) return;

    // Update modal title
    if (modalTitle) {
      modalTitle.textContent = `Understanding ${this.currentVerse.ref}`;
    }

    // Show modal and loading state
    modal.classList.add('active');
    modal.setAttribute('aria-hidden', 'false');
    
    if (loadingDiv) loadingDiv.style.display = 'block';
    if (contentDiv) contentDiv.style.display = 'none';
    if (errorDiv) errorDiv.style.display = 'none';

    // Focus management
    const closeButton = document.getElementById('modal-close');
    if (closeButton) closeButton.focus();

    try {
      let explanation;
      
      // Use pre-computed summary if available
      if (this.currentVerse.summary) {
        explanation = this.currentVerse.summary;
      } else {
        // Fallback to AI call (if API key is available)
        explanation = await this.getAIExplanation();
      }

      this.displayExplanation(explanation);
    } catch (error) {
      console.error('Failed to get explanation:', error);
      this.showExplanationError();
    }
  }

  async getAIExplanation() {
    // This would make an API call to OpenAI if OPENAI_API_KEY is available
    // For now, return a fallback explanation
    throw new Error('AI explanation not available');
  }

  displayExplanation(explanation) {
    const loadingDiv = document.getElementById('explanation-loading');
    const contentDiv = document.getElementById('explanation-content');
    const contextEl = document.getElementById('explanation-context');
    const coreEl = document.getElementById('explanation-core');
    const applicationEl = document.getElementById('explanation-application');

    if (loadingDiv) loadingDiv.style.display = 'none';
    if (contentDiv) contentDiv.style.display = 'block';

    if (contextEl) contextEl.textContent = explanation.context || '';
    if (coreEl) coreEl.textContent = explanation.core || '';
    if (applicationEl) applicationEl.textContent = explanation.application || '';
  }

  showExplanationError() {
    const loadingDiv = document.getElementById('explanation-loading');
    const errorDiv = document.getElementById('explanation-error');

    if (loadingDiv) loadingDiv.style.display = 'none';
    if (errorDiv) errorDiv.style.display = 'block';
  }

  closeModal() {
    const modal = document.getElementById('modal-overlay');
    if (modal) {
      modal.classList.remove('active');
      modal.setAttribute('aria-hidden', 'true');
    }
  }

  async shareVerse() {
    if (!this.currentVerse) return;

    const shareText = `"${this.currentVerse.text}" - ${this.currentVerse.ref}\n\nversaily.com`;

    try {
      await navigator.clipboard.writeText(shareText);
      this.showToast('Verse copied to clipboard');
    } catch (error) {
      console.error('Clipboard access failed:', error);
      this.showToast('Unable to copy to clipboard');
    }
  }

  showToast(message) {
    const toast = document.getElementById('toast');
    const toastMessage = document.getElementById('toast-message');
    
    if (!toast || !toastMessage) return;

    toastMessage.textContent = message;
    toast.classList.add('show');

    setTimeout(() => {
      toast.classList.remove('show');
    }, 3000);
  }

  showError(message) {
    const verseText = document.getElementById('verse-text');
    if (verseText) {
      verseText.textContent = message;
    }
  }
}

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  new DailyVerseApp();
});
