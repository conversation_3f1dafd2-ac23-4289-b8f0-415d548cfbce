{"projectName": "DailyVerse", "description": "A single-page, lightweight daily Bible verse webapp with a unique verse and landscape background each day, an AI 'What does this mean?' popup, and a share button.", "stack": {"frontend": "Vanilla JS + HTML + CSS (no framework) OR Next.js (static export). Keep bundle <30KB gz.", "styling": "Tailwind optional; otherwise minimal CSS variables.", "build": "Pure static recommended. If Next.js, use `next export`."}, "env": {"AI_PROVIDER": "openai", "OPENAI_API_KEY": "set in runtime or as serverless function secret (optional; summaries can also be pre-generated to avoid any runtime cost).", "UNSPLASH_ACCESS_KEY": "optional; only if using Unsplash API. Otherwise use local curated images."}, "routes": [{"path": "/", "type": "single-page", "seo": {"title": "Daily Verse", "description": "A new Bible verse every day with a short, clear explanation.", "ogImage": "/og.jpg"}}], "assets": {"images": {"mode": "local-curated", "folder": "/images/landscapes", "credits": "Add a small footer link for photo credits if required.", "fallback": "/images/landscapes/default.jpg"}, "fonts": {"heading": "system-ui stack", "body": "system-ui stack"}}, "data": {"versesFile": "/data/verses.json", "fields": ["id", "ref", "text", "translation", "tags", "summary"], "notes": "Keep 1 JSON array of all candidate verses. 'summary' can be prefilled to avoid AI calls."}, "dailySelection": {"startDateISO": "2025-01-01", "algorithm": "seeded-shuffle-then-modulo", "seed": "dailyverse-v1", "pseudo": ["const daysSinceStart = Math.floor((todayUTC - new Date(startDateISO)) / 86400000);", "const order = seededFisherYatesShuffle(verses.map(v => v.id), seed); // deterministic, same across users", "const index = daysSinceStart % order.length;", "const todaysId = order[index];", "const verse = versesById[todaysId];"], "reason": "Guarantees no repeats until all verses have been shown, then cycles."}, "backgroundImage": {"strategy": "pair image to verse deterministically", "pseudo": ["const imgIndex = (hash(todaysId + seed) % images.length);", "const img = images[imgIndex];", "apply overlay: linear-gradient(rgba(0,0,0,0.45), rgba(0,0,0,0.45)) over background;"], "alt": "Peaceful landscape background related to the theme."}, "components": [{"name": "DailyVerseHero", "props": ["dateStr", "text", "ref", "onExplain", "onShare"], "ui": {"layout": "full-bleed background image with dark overlay", "typography": {"verse": "large, italic", "ref": "small caps"}, "buttons": [{"label": "What does this mean?", "icon": "sparkles", "action": "onExplain"}, {"label": "Share", "icon": "share", "action": "onShare"}]}}, {"name": "ExplainModal", "props": ["ref", "text", "summary", "close"], "content": [{"section": "Context", "from": "summary.context"}, {"section": "Core Message", "from": "summary.core"}, {"section": "Modern Application", "from": "summary.application"}], "a11y": {"focusTrap": true, "escToClose": true, "ariaLabel": "Understanding this verse"}}, {"name": "Footer", "props": ["photoCredit", "copyright"], "content": "Tiny, unobtrusive."}], "ai": {"mode": "prefer-precomputed", "onDemandFallback": "call OpenAI if summary missing", "openai": {"model": "gpt-4o-mini", "temperature": 0.2, "systemPrompt": "You are a concise, pastoral Bible commentary helper. Stay faithful to the passage. Avoid denominational controversy. Keep it <180 words total across three short sections: Context, Core Message, Modern Application.", "userTemplate": "Explain {{ref}} which says: {{text}}. Output JSON with keys: context, core, application."}}, "share": {"strategy": "Web Share API with copy fallback", "pseudo": ["if (navigator.share) navigator.share({ title: 'Daily Verse – ' + ref, text: text, url: location.href });", "else copyToClipboard(location.href) and show toast 'Link copied'"], "utm": "append ?v={{todaysId}} for stable deep link"}, "state": {"hydratesFrom": ["/data/verses.json", "/data/images.json"], "localStorageKeys": ["dv_seenCycle_v1 (optional)"], "note": "No auth, no cookies. Entire app works offline after first load (optional PWA)."}, "performance": {"targets": {"LCP": "<1.5s", "JS": "<30KB gz", "Images": "lazy + responsive srcset"}, "techniques": ["inline critical CSS", "defer non-critical JS", "image preconnect", "no external UI libs"]}, "accessibility": {"colorContrast": ">= 4.5:1 over overlay", "keyboard": "tab order, buttons focus styles, modal focus trap", "aria": "buttons labelled; modal aria-labelledby"}, "files": [{"path": "/index.html", "type": "html", "note": "Single page shell"}, {"path": "/styles.css", "type": "css", "note": "Minimal styles and overlay"}, {"path": "/app.js", "type": "js", "note": "Implements selection, UI wiring, share, modal, optional AI call"}, {"path": "/data/verses.json", "type": "json", "note": "Verse catalog (can grow over time)"}, {"path": "/data/images.json", "type": "json", "note": "List of landscape images {src, credit}"}], "securityPrivacy": {"analytics": "none by default", "cookies": "none", "aiCalls": "only if OPENAI_API_KEY present; otherwise use local summaries"}, "exampleData": {"verses": [{"id": "1pe5_7_niv", "ref": "1 <PERSON> 5:7", "text": "Cast all your anxiety on him because he cares for you.", "translation": "NIV", "tags": ["anxiety", "comfort", "trust"], "summary": {"context": "<PERSON> writes to believers facing trials, urging humility and trust in God's care.", "core": "Release worry onto <PERSON>; He is attentive and loving toward you.", "application": "Practice prayerful surrender today—name your concerns and entrust them to Him."}}, {"id": "ps23_1_esv", "ref": "Psalm 23:1", "text": "The LORD is my shepherd; I shall not want.", "translation": "ESV", "tags": ["provision", "guidance", "peace"]}, {"id": "phil4_6_nlt", "ref": "Philippians 4:6", "text": "Don’t worry about anything; instead, pray about everything...", "translation": "NLT", "tags": ["prayer", "anxiety", "gratitude"]}, {"id": "isa40_31_niv", "ref": "Isaiah 40:31", "text": "Those who hope in the LORD will renew their strength...", "translation": "NIV", "tags": ["hope", "strength", "perseverance"]}], "images": [{"src": "/images/landscapes/forest-1.jpg", "credit": "Photo by A on B"}, {"src": "/images/landscapes/mountain-1.jpg", "credit": "Photo by C on D"}, {"src": "/images/landscapes/lake-1.jpg", "credit": "Photo by E on F"}]}, "helpers": {"seededFisherYatesShuffle": "Implement PRNG (e.g., mulberry32 from string seed -> int) then standard Fisher<PERSON>Yates using that PRNG.", "hash": "Fast 32-bit string hash (e.g., cyrb53 or djb2) to map verse id to image index.", "formatDate": "Friday, September 12, 2025"}, "uiCopy": {"buttons": {"explain": "What does this mean?", "share": "Share"}, "toasts": {"copied": "Link copied to clipboard"}, "modalTitleTemplate": "Understanding {{ref}}"}, "pwaOptional": {"enable": false, "files": ["/manifest.webmanifest", "/sw.js"], "note": "If enabled, cache verses.json, images.json, and first image for offline."}, "testing": {"e2e": "Verify day-to-index mapping across time zones (use UTC date).", "a11y": "Run axe-core; keyboard-only flow.", "perf": "Lighthouse score >= 95."}}