# Daily Verse - Lightweight Bible Verse Webapp

A single-page, lightweight daily Bible verse webapp that displays a unique verse and landscape background each day, with an AI-powered explanation feature and sharing capabilities.

## Features

- 📖 **Daily Bible Verse**: A new verse every day using a deterministic algorithm
- 🌄 **Beautiful Backgrounds**: Rotating landscape images paired with verses
- ✨ **Verse Explanations**: AI-powered or pre-written explanations of each verse
- 📤 **Easy Sharing**: Web Share API with clipboard fallback
- 📱 **Responsive Design**: Works perfectly on desktop and mobile
- ⚡ **Lightweight**: Under 30KB gzipped, pure static files
- ♿ **Accessible**: Full keyboard navigation and screen reader support

## Quick Start

### Option 1: Simple HTTP Server (Python)
```bash
# Navigate to the project directory
cd versaily

# Start a simple HTTP server
python -m http.server 8000

# Open http://localhost:8000 in your browser
```

### Option 2: Node.js HTTP Server
```bash
# Install a simple HTTP server globally
npm install -g http-server

# Navigate to the project directory and start server
cd versaily
http-server -p 8000

# Open http://localhost:8000 in your browser
```

### Option 3: Live Server (VS Code Extension)
1. Install the "Live Server" extension in VS Code
2. Right-click on `index.html`
3. Select "Open with Live Server"

## Project Structure

```
versaily/
├── index.html          # Main HTML file
├── styles.css          # CSS styling
├── app.js             # JavaScript functionality
├── data/
│   ├── verses.json    # Bible verses with explanations
│   └── images.json    # Image metadata and credits
├── images/
│   └── landscapes/    # Background landscape images
└── README.md          # This file
```

## Adding Content

### Adding New Verses

Edit `data/verses.json` to add new Bible verses:

```json
{
  "id": "unique_verse_id",
  "ref": "Book Chapter:Verse",
  "text": "The verse text...",
  "translation": "NIV",
  "tags": ["theme1", "theme2"],
  "summary": {
    "context": "Historical/literary context...",
    "core": "Main message...",
    "application": "How to apply today..."
  }
}
```

### Adding New Images

1. Add landscape images to `images/landscapes/`
2. Update `data/images.json` with image metadata:

```json
{
  "src": "/images/landscapes/your-image.jpg",
  "credit": "Photo by [Photographer] on [Source]",
  "alt": "Description for accessibility"
}
```

## How It Works

### Daily Verse Selection

The app uses a deterministic algorithm to ensure:
- Every user sees the same verse on the same day
- No verse repeats until all verses have been shown
- The sequence is predictable and consistent

### Algorithm Details

1. Calculate days since start date (2025-01-01)
2. Create a seeded Fisher-Yates shuffle of all verse IDs
3. Use modulo operation to select today's verse
4. Pair verse with image using deterministic hash

### Background Image Pairing

Images are paired with verses using a hash function to ensure:
- Same verse always gets the same background
- Even distribution across all available images
- Deterministic results across all users

## Customization

### Styling

The app uses CSS custom properties for easy theming. Key variables in `styles.css`:

```css
:root {
  --color-overlay: rgba(0, 0, 0, 0.45);
  --font-family: system-ui, -apple-system, ...;
  --font-size-2xl: 2rem;
  /* ... more variables */
}
```

### Configuration

Key settings in `app.js`:

```javascript
this.startDate = new Date('2025-01-01');  // When verse rotation started
this.seed = 'dailyverse-v1';              // Seed for deterministic randomization
```

## Performance

- **Bundle Size**: < 30KB gzipped
- **LCP Target**: < 1.5s
- **No External Dependencies**: Pure vanilla JavaScript
- **Offline Ready**: Works after first load (optional PWA features)

## Browser Support

- Modern browsers with ES6+ support
- Web Share API (with clipboard fallback)
- CSS Grid and Flexbox
- Fetch API

## Accessibility Features

- Semantic HTML structure
- ARIA labels and roles
- Keyboard navigation support
- Focus management in modals
- High contrast mode support
- Reduced motion preferences
- Screen reader compatibility

## Deployment

### Static Hosting

Deploy to any static hosting service:

- **Netlify**: Drag and drop the folder
- **Vercel**: Connect your Git repository
- **GitHub Pages**: Push to a GitHub repository
- **Firebase Hosting**: Use Firebase CLI
- **AWS S3**: Upload as static website

### CDN Optimization

For production:

1. Optimize images (WebP format, compression)
2. Minify CSS and JavaScript
3. Enable gzip compression
4. Set appropriate cache headers
5. Use a CDN for global distribution

## License

This project is open source. Please provide appropriate attribution for any images used and respect their individual licenses.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## Support

For issues or questions, please check the existing issues or create a new one in the repository.
